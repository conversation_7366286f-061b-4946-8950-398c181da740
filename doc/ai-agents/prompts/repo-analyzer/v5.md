## 🧠 Repository Analysis and Feature Metrics Extraction Prompt (LLM-Safe Version)

**SYSTEM INSTRUCTION (MANDATORY):**  
This task requires a **complete analysis of the entire git repository history**.  
You must **never use limiting commands or parameters** that truncate the output of git commands.  
This includes all CLI pipelines, filters, and arguments that restrict commit, branch, or log counts.  
You must process the **entire repository history**.

---

### 🔒 Command Safety Policy

When executing any `git` command:

1. **Always process full history:**
    
    - ✅ `git log --all --decorate --numstat --date=iso`
        
    - ✅ `git branch -r`
        
    - ✅ `git show-ref`
        
    - ✅ `git rev-list --all`
        
2. **Never use limiting operations or truncations:**
    
    - ❌ `| head -N`
        
    - ❌ `| tail -N`
        
    - ❌ `--max-count`
        
    - ❌ `--since` or `--until`
        
    - ❌ `grep -m`
        
    - ❌ Any other partial or sampling operation
        
3. **Never assume recent commits are enough** — always use full repository traversal.
    
4. **Fail validation** if a truncated or filtered command is detected.
    

If truncation is attempted, **stop immediately** and rewrite the command to include full history.

---

### 🎯 Objective

Analyze a local cloned git repository to extract **historical feature development metrics** for use in project estimation models.  
You will produce:

1. A **detailed CSV** (`feature_metrics_analysis.csv`)
    
2. A **summary report** (`repository_analysis_summary.md`)
    
3. A **recommendations report** (`feature_estimation_recommendations.md`)
    

---

### 🔍 Analysis Steps

#### 1. Repository Structure Analysis

- Detect and record the existence of configuration and documentation files:
    
    - `repomix.config.json`, `.augment-guidelines`, `AI_INSTRUCTIONS.md`
        
    - `README.md`, `CONTRIBUTING.md`, `docs/` or `documentation/`
        
    - `.gitflow` configuration or conventions in use
        
- Identify branch naming conventions.
    

#### 2. Git Flow and Branching Analysis

- Extract all branches and detect:
    
    - Feature branches: `feature/JIRA-123-description`
        
    - Release branches: `release/x.x.x`
        
    - Hotfix branches: `hotfix/JIRA-456-description`
        
    - Main development branches: `develop`, `main`, `master`
        
- Detect Jira ticket integration within:
    
    - Branch names
        
    - Commit messages
        
    - Pull request metadata (if available locally)
        

#### 3. Feature Development Tracking

For each feature branch:

|Metric|Description|
|---|---|
|**jira_ticket**|Extracted from branch name or commit message|
|**feature_description**|From branch name, PR title, or first commit|
|**development_start_date**|First commit date|
|**development_end_date**|Last commit or PR merge date|
|**total_dev_days**|End minus start|
|**lines_added / deleted / modified**|From `git log --numstat`|
|**files_changed**|Total unique files|
|**commits_count**|Total commits per branch|
|**complexity_score**|Cyclomatic or derived|
|**team_size**|Unique contributors|
|**developer_names**|List of contributors|
|**review_time_days**|PR open-to-merge duration|
|**testing_time_days**|Estimated time from merge to release|
|**post_release_fixes / rollback_incidents**|From hotfix patterns|

---

### 🧩 Module and Component Identification

Group changes per feature branch by:

- Directory structure (e.g., `frontend/`, `backend/`, `api/`)
    
- File extensions (`.go`, `.ts`, `.jsx`, etc.)
    
- Functional domains (e.g., authentication, payments)
    

---

### 📊 Output CSV Structure

```csv
jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,pr_merge_date,branch_name,complexity_score,team_size,developer_names,review_time_days,testing_time_days,deployment_date,rollback_incidents,post_release_fixes
```


---

### ⚙️ Implementation Directives

#### Git Analysis Commands (STRICT MODE)

**Use these base commands as templates only — do not alter with truncations.**

`git log --all --decorate --numstat --date=iso git branch -r git show-ref git rev-list --all git diff --stat <commit1> <commit2> git blame <file>`

If additional flags are needed, ensure they **do not limit output** or **filter history**.

---

### 📈 Data Processing Workflow

1. **Extract branch metadata**
    
2. **Map commits to features and tickets**
    
3. **Calculate development timelines**
    
4. **Aggregate code change metrics**
    
5. **Compute complexity (if analyzable)**
    
6. **Correlate with Jira data** (optional if API access available)
    
7. **Generate CSV and summary reports**
    

---

### 📏 Validation and Quality Rules

- Verify Jira ticket naming consistency
    
- Detect orphaned or unmerged branches
    
- Identify unusually long/short development cycles
    
- Ensure chronological consistency (start < end)
    
- Flag missing merge information
    

If validation fails due to missing commits, re-run analysis ensuring **no limiting parameters** were used.

---

### 📄 Output Artifacts

1. `feature_metrics_analysis.csv` — detailed per-feature dataset
    
2. `repository_analysis_summary.md` — aggregated metrics and insights
    
3. `feature_estimation_recommendations.md` — suggestions and patterns for future estimates
    

---

### 🧮 Productivity Factor Calculation

For each feature _i_:

`Hi = FH * (team_size_i * total_dev_days_i + review_time_days_i + testing_time_days_i)      + Ef * post_release_fixes_i`

- FH = 6 hours/day
    
- Ef = 4 hours
    

This can later feed into Use Case Point (UCP) estimations.

---

### ✅ Task Execution Rules Summary

|Rule|Must / Must Not|Description|
|---|---|---|
|Use `git log --all`|✅|Always analyze full repo history|
|Use `|head`,`|tail`,` --max-count`,` --since`, etc.|
|Limit commits or branches|❌|Forbidden|
|Process entire repository|✅|Required|
|Abort if truncation detected|✅|Mandatory|
|Generate CSV, summary, and recommendations|✅|Required|

---

### 🚀 Expected CLI Execution Context

This prompt is optimized for execution in environments such as:

- **Copilot CLI**
    
- **Auggie CLI**
    
- **OpenDevin / LangChain Agents**
    
- **Custom local LLM runners**
    

These agents must interpret this prompt as a _full-history analysis contract_ with enforced safety and data integrity rules.