# 🧠 Repository Analysis and Feature Metrics Extraction Prompt (v8 — Deterministic Full History)

**SYSTEM INSTRUCTION (MANDATORY):**  
You are analyzing a local cloned git repository to extract **complete historical feature development metrics**.  
You **MUST process the entire repository history** without any truncation, sampling, or filtering.  
Your output must be **deterministic** and **reproducible** across different AI agents.

## 🎯 PRIMARY OBJECTIVE

Extract **ALL development features** from git history and generate:
1. **`feature_metrics_analysis.csv`** — Complete feature dataset (one row per feature)
2. **`repository_analysis_summary.md`** — Statistical analysis and insights
3. **`validation_report.md`** — Quality assurance and completeness verification

## ⚠️ CRITICAL SUCCESS CRITERIA

### Mandatory Outputs
- **Feature CSV**: Minimum 100+ features for repositories with 500+ commits
- **Coverage**: ≥80% of all meaningful development work captured
- **Quality**: ≥70% features with known module classification
- **Completeness**: Full repository history analyzed (no shallow clones)

### Failure Conditions (MUST Retry)
- Feature count < 50% of expected based on repository size
- >40% features marked as "Unknown" module
- Missing major contributors or time periods
- Validation errors in data consistency

---

## 🔒 EXECUTION PROTOCOL

### Phase 1: Repository Validation (MANDATORY)
```bash
# 1. Ensure full history access
git rev-parse --is-shallow-repository  # Must return 'false'
if [ $? = true ]; then git fetch --unshallow --all --tags; fi

# 2. Collect baseline metrics
TOTAL_COMMITS=$(git rev-list --all | wc -l)
TOTAL_JIRA=$(git log --all --grep='[A-Z]+-[0-9]+' --regexp-ignore-case --oneline | wc -l)
UNIQUE_JIRA=$(git log --all --pretty=format:'%s' | grep -oE '[A-Z]+-[0-9]+' | sort -u | wc -l)
DATE_START=$(git log --all --pretty=format:'%ai' | tail -1)
DATE_END=$(git log --all --pretty=format:'%ai' | head -1)

# 3. Validate repository scope
echo "Repository Validation:"
echo "Total Commits: $TOTAL_COMMITS"
echo "Jira Commits: $TOTAL_JIRA" 
echo "Unique Jira Tickets: $UNIQUE_JIRA"
echo "Date Range: $DATE_START to $DATE_END"
```

### Phase 2: Multi-Method Feature Extraction (MANDATORY)
Execute ALL methods in parallel for maximum coverage:

#### Method A: Jira Ticket Analysis
```bash
# Extract ALL commits with Jira patterns
git log --all --grep='[A-Z]+-[0-9]+' --regexp-ignore-case \
  --pretty=format:'%H|%ai|%an|%s' > jira_commits.txt

# For each unique Jira ticket, aggregate all related commits
git log --all --pretty=format:'%s' | grep -oE '[A-Z]+-[0-9]+' | sort -u | \
while read ticket; do
  git log --all --grep="$ticket" --pretty=format:'%H|%ai|%an|%s' --numstat
done > jira_features.txt
```

#### Method B: Pattern-Based Analysis  
```bash
# Extract substantial development work by commit patterns
PATTERNS=("feat:" "feature:" "add:" "implement:" "fix:" "bug:" "refactor:" "✨" "🚀" "🐛")
for pattern in "${PATTERNS[@]}"; do
  git log --all --grep="$pattern" --pretty=format:'%H|%ai|%an|%s' --numstat
done > pattern_features.txt
```

#### Method C: Branch-Based Analysis
```bash
# Analyze all feature/bugfix/hotfix branches (including deleted)
git for-each-ref --format='%(refname:short)|%(objectname)' refs/remotes/ | \
grep -E 'feature/|bugfix/|hotfix/' > feature_branches.txt

# Extract commits unique to each branch
while IFS='|' read branch commit; do
  git log --pretty=format:'%H|%ai|%an|%s' --numstat "$commit"
done < feature_branches.txt > branch_features.txt
```

#### Method D: Substantial Change Analysis
```bash
# Find commits with significant code changes (>10 lines or >2 files)
git log --all --pretty=format:'%H|%ai|%an|%s' --numstat | \
awk 'BEGIN{FS="\t"} /^[0-9]/ {added+=$1; deleted+=$2; files++} 
     /\|/ {if(added+deleted>10 || files>2) print; added=0; deleted=0; files=0}' \
     > substantial_changes.txt
```

### Phase 3: Data Integration and CSV Generation
```python
# Pseudocode for deterministic processing
features = {}
for method in [jira_features, pattern_features, branch_features, substantial_changes]:
    for commit_group in method:
        feature_id = extract_feature_id(commit_group)  # Jira ticket or generate PATTERN-XXXX
        if feature_id not in features:
            features[feature_id] = initialize_feature(commit_group)
        else:
            features[feature_id] = merge_commits(features[feature_id], commit_group)

# Calculate metrics for each feature
for feature in features.values():
    feature.calculate_metrics()  # dev_days, complexity, team_size, etc.
    feature.determine_module()   # Based on file paths
    
# Generate CSV with deterministic ordering
write_csv(sorted(features.items()))
```

---

## 📊 REQUIRED CSV SCHEMA

**Filename**: `feature_metrics_analysis.csv`

| Column | Type | Description | Calculation |
|--------|------|-------------|-------------|
| `feature_id` | String | Jira ticket (e.g., "ABC-123") or synthetic ID (e.g., "PATTERN-0001") | Primary identifier |
| `feature_description` | String | Clean description from commit message | Remove Jira ID, emojis, prefixes |
| `module_component` | String | Primary module affected | Most frequent directory in file changes |
| `development_start_date` | Date | First commit date | YYYY-MM-DD format |
| `development_end_date` | Date | Last commit date | YYYY-MM-DD format |
| `total_dev_days` | Integer | Development duration | (end_date - start_date) + 1 |
| `lines_added` | Integer | Total lines added | Sum from `git show --numstat` |
| `lines_deleted` | Integer | Total lines deleted | Sum from `git show --numstat` |
| `lines_modified` | Integer | Total lines changed | lines_added + lines_deleted |
| `files_changed` | Integer | Unique files modified | Count of distinct file paths |
| `commits_count` | Integer | Number of commits | Count of commit hashes |
| `complexity_score` | Float | Estimated complexity (1-10) | Formula: min(10, files×0.5 + lines/100 + commits×0.3) |
| `team_size` | Integer | Number of contributors | Count of unique authors |
| `developer_names` | String | Comma-separated authors | Sorted alphabetically |
| `branch_name` | String | Associated branch | From branch analysis or "Unknown" |
| `is_jira_tracked` | Boolean | Has Jira ticket | true/false |
| `feature_type` | String | Category | "feature", "bugfix", "refactor", "other" |

---

## 📋 MODULE CLASSIFICATION RULES

**Deterministic module assignment** based on file path patterns:

```python
def determine_module(file_paths):
    module_counts = Counter()
    for path in file_paths:
        if path.startswith('app/'):
            module_counts[f"app/{path.split('/')[1]}"] += 1
        elif path.startswith(('config/', 'database/', 'routes/', 'tests/')):
            module_counts[path.split('/')[0]] += 1
        elif path.startswith('public/'):
            module_counts['frontend'] += 1
        elif path.endswith(('.js', '.ts', '.jsx', '.tsx', '.vue')):
            module_counts['frontend'] += 1
        elif path.endswith(('.php', '.py', '.java', '.go', '.rb')):
            module_counts['backend'] += 1
        else:
            module_counts['other'] += 1
    
    return module_counts.most_common(1)[0][0] if module_counts else 'Unknown'
```

## 🎯 FEATURE TYPE CLASSIFICATION

```python
def classify_feature_type(description, file_paths):
    desc_lower = description.lower()
    if any(word in desc_lower for word in ['feat', 'feature', 'add', 'implement', 'new']):
        return 'feature'
    elif any(word in desc_lower for word in ['fix', 'bug', 'hotfix', 'patch']):
        return 'bugfix'  
    elif any(word in desc_lower for word in ['refactor', 'restructure', 'cleanup']):
        return 'refactor'
    elif any(path.startswith('test') for path in file_paths):
        return 'testing'
    else:
        return 'other'
```

## ✅ VALIDATION REQUIREMENTS

### Automated Quality Checks
```bash
# 1. Feature count validation
FEATURE_COUNT=$(tail -n +2 feature_metrics_analysis.csv | wc -l)
EXPECTED_MIN=$((TOTAL_COMMITS / 10))  # Heuristic: 1 feature per 10 commits
if [ $FEATURE_COUNT -lt $EXPECTED_MIN ]; then
    echo "ERROR: Feature count too low ($FEATURE_COUNT < $EXPECTED_MIN)"
    exit 1
fi

# 2. Module distribution validation  
UNKNOWN_COUNT=$(cut -d',' -f3 feature_metrics_analysis.csv | grep -c "Unknown")
UNKNOWN_PERCENT=$((UNKNOWN_COUNT * 100 / FEATURE_COUNT))
if [ $UNKNOWN_PERCENT -gt 40 ]; then
    echo "ERROR: Too many unknown modules ($UNKNOWN_PERCENT%)"
    exit 1
fi

# 3. Date range validation
CSV_START=$(tail -n +2 feature_metrics_analysis.csv | cut -d',' -f4 | sort | head -1)
CSV_END=$(tail -n +2 feature_metrics_analysis.csv | cut -d',' -f5 | sort | tail -1)
echo "CSV date range: $CSV_START to $CSV_END"
echo "Git date range: $DATE_START to $DATE_END"
```

### Required Validation Report
Generate `validation_report.md` with:
- ✅ Completeness score: (Features extracted / Expected features) × 100
- ✅ Quality score: (Known modules / Total features) × 100  
- ✅ Coverage score: (Date range covered / Repository age) × 100
- ✅ Contributor coverage: All major contributors represented
- ✅ Data consistency: No missing required fields

---

## 📊 SUMMARY REPORT REQUIREMENTS

**Filename**: `repository_analysis_summary.md`

### Required Sections
1. **Repository Overview** - commits, contributors, date range
2. **Feature Extraction Results** - total features, coverage percentage
3. **Development Metrics** - avg dev time, complexity distribution
4. **Team Analysis** - team sizes, top contributors
5. **Module Distribution** - component breakdown
6. **Estimation Guidelines** - time ranges by complexity

### Statistical Analysis
```python
# Required calculations for summary
avg_dev_days = df['total_dev_days'].mean()
median_dev_days = df['total_dev_days'].median()
complexity_distribution = df['complexity_score'].describe()
team_size_distribution = df['team_size'].value_counts()
module_distribution = df['module_component'].value_counts()
feature_type_distribution = df['feature_type'].value_counts()
```

---

## 🚀 EXECUTION CHECKLIST

### Pre-Execution ✅
- [ ] Repository is not shallow (`git rev-parse --is-shallow-repository` = false)
- [ ] All remote branches fetched (`git fetch --all --tags --prune`)
- [ ] Baseline metrics collected and validated

### During Execution ✅  
- [ ] All 4 extraction methods executed
- [ ] Data merged and deduplicated
- [ ] CSV generated with all required columns
- [ ] Validation checks passed

### Post-Execution ✅
- [ ] `feature_metrics_analysis.csv` created (100+ features expected)
- [ ] `repository_analysis_summary.md` generated
- [ ] `validation_report.md` created with passing scores
- [ ] All files are non-empty and properly formatted

### Success Criteria ✅
- [ ] Feature count ≥ 80% of expected based on repository size
- [ ] Module classification ≥ 70% known (≤30% "Unknown")
- [ ] Date range covers full repository history
- [ ] All major contributors represented
- [ ] No validation errors or warnings

---

## 🎯 DETERMINISTIC EXECUTION GUARANTEE

This prompt is designed for **consistent, reproducible results** across different AI agents by:

1. **Explicit command sequences** - No ambiguous instructions
2. **Validation checkpoints** - Clear pass/fail criteria  
3. **Deterministic algorithms** - Consistent feature classification
4. **Complete coverage** - No sampling or truncation allowed
5. **Quality gates** - Automatic retry on validation failure

**Expected Runtime**: 5-15 minutes for repositories with 1,000-10,000 commits.
**Expected Output**: 100-1,000+ features depending on repository size and activity.

---

## 🔧 IMPLEMENTATION TEMPLATE

### Python Script Structure
```python
#!/usr/bin/env python3
"""
Deterministic Feature Extraction Implementation
"""
import subprocess, csv, re, json
from datetime import datetime
from collections import Counter, defaultdict

class DeterministicFeatureExtractor:
    def __init__(self):
        self.features = {}
        self.validation_metrics = {}

    def validate_repository(self):
        """Phase 1: Repository validation"""
        # Implementation here

    def extract_jira_features(self):
        """Method A: Jira ticket analysis"""
        # Implementation here

    def extract_pattern_features(self):
        """Method B: Pattern-based analysis"""
        # Implementation here

    def extract_branch_features(self):
        """Method C: Branch-based analysis"""
        # Implementation here

    def extract_substantial_changes(self):
        """Method D: Substantial change analysis"""
        # Implementation here

    def merge_and_deduplicate(self):
        """Combine all extraction methods"""
        # Implementation here

    def calculate_metrics(self):
        """Calculate all feature metrics"""
        # Implementation here

    def generate_csv(self):
        """Generate feature_metrics_analysis.csv"""
        # Implementation here

    def generate_summary(self):
        """Generate repository_analysis_summary.md"""
        # Implementation here

    def validate_output(self):
        """Generate validation_report.md"""
        # Implementation here

def main():
    extractor = DeterministicFeatureExtractor()
    extractor.validate_repository()
    extractor.extract_jira_features()
    extractor.extract_pattern_features()
    extractor.extract_branch_features()
    extractor.extract_substantial_changes()
    extractor.merge_and_deduplicate()
    extractor.calculate_metrics()
    extractor.generate_csv()
    extractor.generate_summary()
    extractor.validate_output()

if __name__ == "__main__":
    main()
```

### Bash Script Alternative
```bash
#!/bin/bash
# Deterministic Feature Extraction Script

# Phase 1: Validation
validate_repository() {
    echo "🔍 Validating repository..."
    # Implementation
}

# Phase 2: Extraction
extract_all_features() {
    echo "🎯 Extracting features..."
    # Implementation
}

# Phase 3: Processing
process_and_generate() {
    echo "📊 Processing data..."
    # Implementation
}

# Main execution
main() {
    validate_repository
    extract_all_features
    process_and_generate
    echo "✅ Feature extraction completed successfully"
}

main "$@"
```

---

*This prompt guarantees deterministic, high-quality feature extraction suitable for estimation modeling and development analytics.*
