# 🧠 Repository Analysis and Feature Metrics Extraction Prompt (v2 — Full History Safe)

**SYSTEM INSTRUCTION (MANDATORY):**  
You are analyzing a local cloned git repository to extract **historical feature development metrics**.  
You **must never truncate or limit git history in any way**.  
You must **process the entire repository history**, including **deleted**, **merged**, and **unmerged** branches.  
Your output must include **every detectable feature**, not just those still visible in remote branches.

---

## 🔒 Command Safety Policy

### 🚫 Forbidden Operations

You must **never** use commands that restrict, filter, or sample repository data:

- `| head`, `| tail`, `| grep -m`, `--max-count`, `--since`, `--until`
    
- Any `awk`, `sed`, or pipeline that limits lines
    
- Any form of sampling, pagination, or truncation
    
- Any `git log` or `git branch` command with count or time filters
    

### ✅ Allowed Commands

Use **complete-history** commands only:

```
git fetch --all --tags --prune
git fetch --unshallow || true
git log --all --decorate --numstat --date=iso
git rev-list --all --remotes --branches --tags
git show-ref
git branch -r
git reflog show --all
git log --all --merges --grep='feature/' --grep='[A-Z]\+-[0-9]\+' --regexp-ignore-case

```

### ❗ Integrity Validation

Before starting analysis:

1. Confirm the repository is **not shallow**:
```
git rev-parse --is-shallow-repository

```

If `true`, run:

```
git fetch --unshallow --tags --all
```

1. Ensure you analyze all commits:
```
git rev-list --all | wc -l

```

Store this value as `total_commits_analyzed`.

## 🎯 Objective

Analyze the full git repository to extract **feature-level development metrics** for use in future estimation models.  
Produce:

1. `feature_metrics_analysis.csv` — detailed feature-level data
    
2. `repository_analysis_summary.md` — aggregated insights
    
3. `feature_estimation_recommendations.md` — recommendations for estimation


## 🧩 Repository Structure Analysis

Identify repository configuration and structure files:

- `repomix.config.json`, `.augment-guidelines`, `AI_INSTRUCTIONS.md`
    
- `README.md`, `CONTRIBUTING.md`, `docs/`, `documentation/`
    
- `.gitflow`, `.github/`, or similar workflow metadata
    
- Branch naming conventions
    

Output detected structure as a summary table in the report.


---


## 🔀 Git Flow and Branching Analysis

Examine **all historical branches** (existing, deleted, merged, unmerged):

Detect:

- `feature/*` branches
    
- `release/*` branches
    
- `hotfix/*` branches
    
- `develop`, `main`, `master`, and others
    

Check for Jira ticket IDs in:

- Branch names
    
- Commit messages
    
- Merge commit titles and PR metadata
    

Use **case-insensitive** Jira regex:

```
(?i)([A-Z]{2,10}-\d+)
```


---

## ⚙️ Feature Development Tracking

For **each feature branch**, or reconstructed feature (even if deleted):

|Metric|Description|
|---|---|
|**jira_ticket**|Extracted from branch or commits|
|**feature_description**|From branch, PR title, or first commit|
|**development_start_date**|First commit timestamp|
|**development_end_date**|Last commit or merge date|
|**total_dev_days**|End minus start|
|**lines_added / deleted / modified**|From `git log --numstat`|
|**files_changed**|Unique files changed|
|**commits_count**|Commits per feature|
|**complexity_score**|Cyclomatic or proxy metric|
|**team_size**|Unique commit authors|
|**developer_names**|List of contributors|
|**review_time_days**|Time from PR open to merge|
|**testing_time_days**|Estimated test period|
|**rollback_incidents / post_release_fixes**|Derived from `hotfix/*`|

### Include:

- Reconstructed deleted branches (from merge commits, reflog, or Jira IDs)
    
- Unmerged branches (still local or abandoned)
    
- Commits containing Jira IDs without branch references
    
- Experimental or feature flags branches if pattern matched
    

---

## 🧮 Data Processing Workflow

1. **Extract all branch & commit data**
    
    - Use `git rev-list --all --remotes --branches --tags`
        
    - Include reflog and lost commits (`git fsck --lost-found` optional)
        
2. **Reconstruct features**
    
    - Group commits by feature branch name or Jira ticket
        
    - If branch deleted, reconstruct via merge commit ancestry or Jira match
        
3. **Compute metrics**
    
    - Start/end dates, commit counts, lines added/deleted, files changed
        
    - Team composition and development time
        
    - Estimate complexity (optional: `radon`, `gocyclo`)
        
4. **Aggregate and enrich**
    
    - Combine data into per-feature rows
        
    - Compute productivity factor (PF)
        
    - Detect anomalies (short/long dev cycles, rollback rates)
        

---

## 🧾 Output: CSV Structure


```
jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,pr_merge_date,branch_name,complexity_score,team_size,developer_names,review_time_days,testing_time_days,deployment_date,rollback_incidents,post_release_fixes

```

### Additional Columns (Optional)

- `test_coverage_change`
    
- `dependencies_added`
    
- `breaking_changes`
    
- `story_points`
    
- `priority_level`
    
- `epic_name`
    
- `release_version`

### 💡 Productivity Factor Calculation

```
Hi = FH * (team_size_i * total_dev_days_i + review_time_days_i + testing_time_days_i)
     + Ef * post_release_fixes_i

```

Constants:

- `FH = 6 hours/day`
    
- `Ef = 4 hours`

---

## 🧩 Module & Component Identification

Group changes by:

- Top-level directories (e.g., `frontend/`, `backend/`, `api/`)
    
- File types (`.go`, `.ts`, `.jsx`, `.py`, etc.)
    
- Functional modules (authentication, reporting, etc.)

---

## ✅ Validation & Completeness Rules

After generating CSV:

1. Count detected features: 
```
detected_features=$(grep -E -i 'feature/' <<< "$(git log --all --oneline)" | wc -l)

```
- Validate completeness:
    
    - If `CSV_features < 0.8 * detected_features`, rerun analysis.
        
    - If missing Jira tickets found in log not present in CSV → rerun.
        
- Ensure every Jira ID in `git log --all` exists in the final CSV.
    
- Validate chronological consistency: start < end.
    
- Confirm no truncated history (total commits match `git rev-list --all | wc -l`).

---

## 📤 Output Artifacts

1. **`feature_metrics_analysis.csv`** — detailed per-feature dataset
    
    - Append results incrementally to avoid output truncation.
        
    - Do not hold all data in memory before writing.
        
2. **`repository_analysis_summary.md`** — summary of findings
    
    - Include total features, average development time, complexity trends, top contributors.
        
3. **`feature_estimation_recommendations.md`** — recommendations for future estimates
    
    - Feature duration ranges, team productivity averages, detected risk patterns.
        

---

## 🧱 Rules Recap Table

|Rule|Must / Must Not|Description|
|---|---|---|
|Use `git log --all`|✅|Always analyze full history|
|Include deleted branches|✅|Reconstruct from merges|
|Include unmerged branches|✅|Capture orphaned work|
|Use Jira regex `(?i)([A-Z]{2,10}-\d+)`|✅|Detect case-insensitive|
|Use pipelines like `|head`|❌|
|Limit commits, branches, or logs|❌|Forbidden|
|Stream CSV output incrementally|✅|Required|
|Validate completeness ratio ≥ 80%|✅|Required|
|Abort if shallow repo|✅|Mandatory fetch full history|
|Use total commit validation|✅|Required|

---

## 🚀 Execution Context

This prompt is optimized for automated CLI-based agents with local repo access, such as:

- **Copilot CLI**
    
- **Auggie CLI**
    
- **OpenDevin**
    
- **LangChain ReAct agents**
    
- **Custom Python or Node CLI tools**
    

Agents must interpret this as a **non-truncated full-history analysis contract**  
and are expected to handle large git histories efficiently with streaming outputs.