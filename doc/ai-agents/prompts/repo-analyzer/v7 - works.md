# 🧠 Repository Analysis and Feature Metrics Extraction Prompt (v2 — Full History Safe)

**SYSTEM INSTRUCTION (MANDATORY):**  
You are analyzing a local cloned git repository to extract **historical feature development metrics**.  
You **must never truncate or limit git history in any way**.  
You must **process the entire repository history**, including **deleted**, **merged**, and **unmerged** branches.  
Your output must include **every detectable feature**, not just those still visible in remote branches.

## ⚠️ CRITICAL REQUIREMENT

The analysis MUST extract features from ALL commits containing Jira tickets, regardless of branch structure. 
Feature count MUST be ≥70% of total Jira tickets found in repository. 
If initial analysis yields <70% coverage, MUST expand extraction methods until complete coverage achieved.


## 🔍 Completeness Validation Requirements

### Pre-Analysis Validation
Before starting feature extraction, MUST validate:
1. **Total Jira tickets**: `git log --all --grep='[A-Z]+-[0-9]+' --regexp-ignore-case | wc -l`
2. **Expected feature count**: Should be 80-95% of total Jira tickets found
3. **Commit coverage**: Ensure analysis covers commits from ALL branches, not just feature branches

### Post-Analysis Validation
After generating CSV, MUST verify:
- **Feature count ≥ 80% of total Jira tickets** found in repository
- **Date range spans entire repository history**
- **All major contributors appear** in the dataset
- **Module distribution is realistic** (not dominated by "Unknown" or "Other")

### Failure Criteria
If any validation fails, MUST re-run analysis with expanded scope.

---

## 🔒 Command Safety Policy

## 🚫 ABSOLUTE PROHIBITIONS 

### Forbidden Analysis Limitations
- ❌ NEVER limit to feature branches only
- ❌ NEVER exclude commits on develop/main/master
- ❌ NEVER sample or limit Jira ticket analysis
- ❌ NEVER use `--max-count`, `--since`, `--until` without explicit user request
- ❌ NEVER assume features only exist in dedicated branches

### Required Scope
- ✅ MUST analyze ALL commits with Jira tickets
- ✅ MUST include direct commits to main branches
- ✅ MUST capture features without dedicated branches
- ✅ MUST validate final count against total repository activity

---



### 🚫 Forbidden Operations

You must **never** use commands that restrict, filter, or sample repository data:

- `| head`, `| tail`, `| grep -m`, `--max-count`, `--since`, `--until`
    
- Any `awk`, `sed`, or pipeline that limits lines
    
- Any form of sampling, pagination, or truncation
    
- Any `git log` or `git branch` command with count or time filters
    

### ✅ Allowed Commands

Use **complete-history** commands only:

```
git fetch --all --tags --prune
git fetch --unshallow || true
git log --all --decorate --numstat --date=iso
git rev-list --all --remotes --branches --tags
git show-ref
git branch -r
git reflog show --all
git log --all --merges --grep='feature/' --grep='[A-Z]\+-[0-9]\+' --regexp-ignore-case

```

### ❗ Integrity Validation

Before starting analysis:

1. Confirm the repository is **not shallow**:
```
git rev-parse --is-shallow-repository

```

If `true`, run:

```
git fetch --unshallow --tags --all
```

1. Ensure you analyze all commits:
```
git rev-list --all | wc -l

```

Store this value as `total_commits_analyzed`.

## 🎯 Objective

Analyze the full git repository to extract **feature-level development metrics** for use in future estimation models.  
Produce:

1. `feature_metrics_analysis.csv` — detailed feature-level data
    
2. `repository_analysis_summary.md` — aggregated insights
    
3. `feature_estimation_recommendations.md` — recommendations for estimation


## 🧩 Repository Structure Analysis

Identify repository configuration and structure files:

- `repomix.config.json`, `.augment-guidelines`, `AI_INSTRUCTIONS.md`
    
- `README.md`, `CONTRIBUTING.md`, `docs/`, `documentation/`
    
- `.gitflow`, `.github/`, or similar workflow metadata
    
- Branch naming conventions
    

Output detected structure as a summary table in the report.


---


## 🔀 Git Flow and Branching Analysis

Examine **all historical branches** (existing, deleted, merged, unmerged):

Detect:

- `feature/*` branches
    
- `release/*` branches
    
- `hotfix/*` branches
    
- `develop`, `main`, `master`, and others
    

Check for Jira ticket IDs in:

- Branch names
    
- Commit messages
    
- Merge commit titles and PR metadata
    

Use **case-insensitive** Jira regex:

```
(?i)([A-Z]{2,10}-\d+)
```


---

## ⚙️ Feature Development Tracking

For **each feature branch**, or reconstructed feature (even if deleted):

|Metric|Description|
|---|---|
|**jira_ticket**|Extracted from branch or commits|
|**feature_description**|From branch, PR title, or first commit|
|**development_start_date**|First commit timestamp|
|**development_end_date**|Last commit or merge date|
|**total_dev_days**|End minus start|
|**lines_added / deleted / modified**|From `git log --numstat`|
|**files_changed**|Unique files changed|
|**commits_count**|Commits per feature|
|**complexity_score**|Cyclomatic or proxy metric|
|**team_size**|Unique commit authors|
|**developer_names**|List of contributors|
|**review_time_days**|Time from PR open to merge|
|**testing_time_days**|Estimated test period|
|**rollback_incidents / post_release_fixes**|Derived from `hotfix/*`|

### Include:

- Reconstructed deleted branches (from merge commits, reflog, or Jira IDs)
    
- Unmerged branches (still local or abandoned)
    
- Commits containing Jira IDs without branch references
    
- Experimental or feature flags branches if pattern matched
    

---

## 🧮 Data Processing Workflow

1. **Extract all branch & commit data**
    
    - Use `git rev-list --all --remotes --branches --tags`
        
    - Include reflog and lost commits (`git fsck --lost-found` optional)
        
2. **Reconstruct features**
    
    - Group commits by feature branch name or Jira ticket
        
    - If branch deleted, reconstruct via merge commit ancestry or Jira match
        
3. **Compute metrics**
    
    - Start/end dates, commit counts, lines added/deleted, files changed
        
    - Team composition and development time
        
    - Estimate complexity (optional: `radon`, `gocyclo`)
        
4. **Aggregate and enrich**
    
    - Combine data into per-feature rows
        
    - Compute productivity factor (PF)
        
    - Detect anomalies (short/long dev cycles, rollback rates)
        

## 🔄 Mandatory Analysis Workflow

### Step 1: Repository Reconnaissance
1. Count total commits: `git rev-list --all | wc -l`
2. Count Jira tickets: `git log --all --grep='[A-Z]+-[0-9]+' --regexp-ignore-case | wc -l`
3. Identify date range: `git log --all --pretty=format:'%ai' | head -1` and `tail -1`
4. List top contributors: `git shortlog -sn --all | head -10`

### Step 2: Multi-Method Extraction
1. Extract branch-based features
2. Extract Jira-based features (ALL commits with tickets)
3. Extract pattern-based features
4. Merge and deduplicate results

### Step 3: Validation and Quality Check
1. Verify feature count vs. Jira tickets
2. Check date coverage
3. Validate contributor representation
4. Ensure module diversity

### Step 4: Iterative Improvement
If validation fails:
1. Expand extraction methods
2. Lower filtering thresholds
3. Include more commit patterns
4. Re-run until validation passes


## 🎯 Multi-Method Feature Extraction (MANDATORY)

MUST use ALL of these methods to ensure complete coverage:

### Method 1: Branch-Based Analysis
- Analyze feature/bugfix/hotfix branches
- Extract commits unique to each branch

### Method 2: Jira-Based Analysis  
- Extract ALL commits containing Jira patterns: `(?i)([A-Z]{2,10}-\d+)`
- Group commits by Jira ticket regardless of branch
- Include commits on develop/main/master branches

### Method 3: Pattern-Based Analysis
- Extract commits with feature patterns: `feat|feature|add|implement|✨|🚀|new`
- Extract commits with fix patterns: `fix|bug|🐛|hotfix`
- Extract commits with refactor patterns: `refactor|♻️|restructure`

### Method 4: Direct Commit Analysis
- Scan ALL commits for meaningful changes (>5 lines modified)
- Group by author and date proximity for orphaned features

### Integration Requirement
Combine results from ALL methods, deduplicate, and ensure no features are missed.

---

## 🧾 Output: CSV Structure


```
jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,pr_merge_date,branch_name,complexity_score,team_size,developer_names,review_time_days,testing_time_days,deployment_date,rollback_incidents,post_release_fixes

```

### Additional Columns (Optional)

- `test_coverage_change`
    
- `dependencies_added`
    
- `breaking_changes`
    
- `story_points`
    
- `priority_level`
    
- `epic_name`
    
- `release_version`

### 💡 Productivity Factor Calculation

```
Hi = FH * (team_size_i * total_dev_days_i + review_time_days_i + testing_time_days_i)
     + Ef * post_release_fixes_i

```

Constants:

- `FH = 6 hours/day`
    
- `Ef = 4 hours`

---

## 🧩 Module & Component Identification

Group changes by:

- Top-level directories (e.g., `frontend/`, `backend/`, `api/`)
    
- File types (`.go`, `.ts`, `.jsx`, `.py`, etc.)
    
- Functional modules (authentication, reporting, etc.)

---

## ✅ Validation & Completeness Rules

After generating CSV:

1. Count detected features: 
```
detected_features=$(grep -E -i 'feature/' <<< "$(git log --all --oneline)" | wc -l)

```
- Validate completeness:
    
    - If `CSV_features < 0.8 * detected_features`, rerun analysis.
        
    - If missing Jira tickets found in log not present in CSV → rerun.
        
- Ensure every Jira ID in `git log --all` exists in the final CSV.
    
- Validate chronological consistency: start < end.
    
- Confirm no truncated history (total commits match `git rev-list --all | wc -l`).

---

## 📤 Output Artifacts

1. **`feature_metrics_analysis.csv`** — detailed per-feature dataset
    
    - Append results incrementally to avoid output truncation.
        
    - Do not hold all data in memory before writing.
        
2. **`repository_analysis_summary.md`** — summary of findings
    
    - Include total features, average development time, complexity trends, top contributors.
        
3. **`feature_estimation_recommendations.md`** — recommendations for future estimates
    
    - Feature duration ranges, team productivity averages, detected risk patterns.
        

## 📤 Enhanced Output Requirements

### Primary CSV Requirements
- **Minimum feature count**: 70% of total Jira tickets found
- **Complete date coverage**: From first to last commit
- **Comprehensive modules**: <30% "Unknown" classification
- **Full contributor coverage**: All major contributors represented

### Validation Report Required
Must include:
- Total Jira tickets found vs. features extracted
- Coverage percentage and validation status
- Date range validation
- Module distribution analysis
- Contributor coverage verification

### Quality Metrics
- **Completeness Score**: (Features extracted / Jira tickets found) × 100
- **Coverage Score**: (Date range covered / Total repository age) × 100
- **Quality Score**: (Known modules / Total features) × 100

---

## 📊 Expected Output Validation

### Realistic Feature Counts
For a repository with:
- 4,000+ commits: Expect 500-1,000+ features
- 500+ Jira tickets: Expect 400-500+ features in CSV
- 2+ years of history: Expect 200-500+ features minimum

### Quality Indicators
- **Feature count should be 70-95%** of unique Jira tickets
- **Date range should span** entire repository history
- **Module distribution should be diverse** (not >50% "Unknown")
- **Team distribution should reflect** actual contributors

### Red Flags (Require Re-Analysis)
- Feature count < 50% of Jira tickets found
- >30% of features marked as "Unknown" module
- Missing major time periods in development history
- Top contributors missing from feature list

---

## 🧱 Rules Recap Table

|Rule|Must / Must Not|Description|
|---|---|---|
|Use `git log --all`|✅|Always analyze full history|
|Include deleted branches|✅|Reconstruct from merges|
|Include unmerged branches|✅|Capture orphaned work|
|Use Jira regex `(?i)([A-Z]{2,10}-\d+)`|✅|Detect case-insensitive|
|Use pipelines like `|head`|❌|
|Limit commits, branches, or logs|❌|Forbidden|
|Stream CSV output incrementally|✅|Required|
|Validate completeness ratio ≥ 80%|✅|Required|
|Abort if shallow repo|✅|Mandatory fetch full history|
|Use total commit validation|✅|Required|

---

## 🎯 Multi-Method Feature Extraction (MANDATORY)

MUST use ALL of these methods to ensure complete coverage:

### Method 1: Branch-Based Analysis
- Analyze feature/bugfix/hotfix branches
- Extract commits unique to each branch

### Method 2: Jira-Based Analysis  
- Extract ALL commits containing Jira patterns: `(?i)([A-Z]{2,10}-\d+)`
- Group commits by Jira ticket regardless of branch
- Include commits on develop/main/master branches

### Method 3: Pattern-Based Analysis
- Extract commits with feature patterns: `feat|feature|add|implement|✨|🚀|new`
- Extract commits with fix patterns: `fix|bug|🐛|hotfix`
- Extract commits with refactor patterns: `refactor|♻️|restructure`

### Method 4: Direct Commit Analysis
- Scan ALL commits for meaningful changes (>5 lines modified)
- Group by author and date proximity for orphaned features

### Integration Requirement
Combine results from ALL methods, deduplicate, and ensure no features are missed.

---

## 🚀 Execution Context

This prompt is optimized for automated CLI-based agents with local repo access, such as:

- **Copilot CLI**
    
- **Auggie CLI**
    
- **OpenDevin**
    
- **LangChain ReAct agents**
    
- **Custom Python or Node CLI tools**
    

Agents must interpret this as a **non-truncated full-history analysis contract**  
and are expected to handle large git histories efficiently with streaming outputs.