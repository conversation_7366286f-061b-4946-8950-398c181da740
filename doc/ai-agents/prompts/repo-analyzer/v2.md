## Repository Analysis and Feature Metrics Extraction Prompt

Analyze this git repository to extract historical feature development data following the instructions below. 

### Objective

Analyze a git repository to extract historical feature development data for use in future project estimations, including development timelines, code complexity, and Jira ticket associations.

### Analysis Steps

#### 1. Repository Structure Analysis

- Scan the repository root for configuration and documentation files:
    - `repomix.config.json` or similar repomix configuration
    - `.augment-guidelines`, `AI_INSTRUCTIONS.md`, or similar AI agent instructions
    - `README.md`, `CONTRIBUTING.md`, documentation folders (`docs/`, `documentation/`)
    - `.gitflow` configuration or evidence of git flow usage
    - Branch naming conventions and patterns

#### 2. Git Flow and Branching Analysis

- Examine branch history to identify:
    - Feature branches following pattern: `feature/JIRA-123-description`
    - Release branches: `release/x.x.x`
    - Hotfix branches: `hotfix/JIRA-456-description`
    - Development branch usage (`develop`, `main`, `master`)
- Validate Jira ticket integration in:
    - Branch names
    - Commit messages
    - Pull request titles and descriptions

#### 3. Feature Development Tracking

For each feature branch, extract:

- **Jira Ticket ID**: From branch name or commit messages
- **Feature Description**: From branch name, PR title, or commit messages
- **Development Timeline**:
    - Branch creation date
    - First commit date
    - Last commit date
    - PR creation date
    - PR merge date
    - Total development time (days/hours)
- **Code Metrics**:
    - Lines of code added/modified/deleted
    - Number of files changed
    - Number of commits
    - Complexity indicators (if available)

#### 4. Module and Component Identification

Based on repository structure and file changes:

- Identify main modules/components affected by each feature
- Group changes by:
    - Directory structure (e.g., `frontend/`, `backend/`, `api/`)
    - File types (`.go`, `.ts`, `.jsx`, etc.)
    - Functional areas (authentication, user management, reporting)

### Output CSV Structure


```csv
jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,pr_merge_date,branch_name,complexity_score,team_size,developer_names,review_time_days,testing_time_days,deployment_date,rollback_incidents,post_release_fixes
```


### Recommended Additional Columns

1. **Team Metrics**:
    - `team_size`: Number of developers involved
    - `developer_names`: List of contributors
    - `primary_developer`: Main contributor
2. **Quality Metrics**:
    - `review_time_days`: Time from PR creation to approval
    - `testing_time_days`: Time spent in testing phases
    - `post_release_fixes`: Number of hotfixes after release
    - `rollback_incidents`: Any rollbacks required
3. **Technical Metrics**:
    - `complexity_score`: Cyclomatic complexity or similar metric
    - `test_coverage_change`: Impact on test coverage
    - `dependencies_added`: New dependencies introduced
    - `breaking_changes`: Boolean for breaking changes
4. **Business Metrics**:
    - `story_points`: If available from Jira
    - `priority_level`: Feature priority
    - `epic_name`: Parent epic if applicable
    - `release_version`: Target release version

### Implementation Approach


#### Tasks to consider:

- [] check for configuration files and documentation
- [] examine the git history and branch structure
- [] get more detailed git history and check for feature branches
- [] get more comprehensive branch and commit information
- [] create a comprehensive script to analyze the git repository and extract feature development metrics
- [] check the generated CSV file and create a comprehensive summary report
- [] create a comprehensive summary report
- [] create an enhanced analysis script that provides additional insights and generates a more detailed CSV with additional metrics
- [] run the enhanced analysis
- [] check the enhanced CSV file and create a final validation report
- [] clean up the analysis script as requested and create a final summary report
- [] create a final validation of the generated files
- [] create a final summary report


#### Data Processing Steps:

1. **Extract branch information**: Parse all feature branches and their metadata
2. **Correlate commits**: Link commits to branches and extract metrics
3. **Calculate timelines**: Compute development duration and phases
4. **Aggregate metrics**: Sum up code changes per feature/module
5. **Enrich with Jira data**: If Jira API access available, fetch story points and priorities
6. **Generate insights**: Calculate averages, identify patterns, flag outliers
7. **Calculate Productivity Factor (PF)**: calculate Productivity Factor (PF) for later use in Use Case Points (UCP) calculation estimation. This is the formula
    - Hi​=FH⋅(team_sizei​⋅total_dev_daysi​+review_time_daysi​+testing_time_daysi​)+Ef​⋅post_release_fixesi​
        - Suggested constants: FH=6FH=6FH=6 hours/day, Ef=4E_f=4Ef​=4 hours.


### Validation and Quality Checks

- Verify Jira ticket format consistency
- Check for orphaned commits not associated with features
- Identify incomplete features (branches without merges)
- Flag unusually long or short development cycles
- Validate date sequences (start < end dates)

### Output Format

Generate files:

1. **Detailed CSV**: Complete feature-level data (feature_metrics_analysis.csv)
2. **Summary Report**: Aggregated statistics and insights (repository_analysis_summary.md)
3. **Recommendations**: Patterns and suggestions for future estimations (feature_estimation_recommendations.md)

