---
title: "SteerCo Metrics"
categories:
  - Position
  - Architecture
  - Role
menu:
  main:
    name: SteerCo Metrics
    parent: Architecture Handbook
---

# SteerCo Metrics

## Introduction

This document provides a structured approach to tracking and analyzing key performance indicators (KPIs) for the Steering Committee (SteerCo). It outlines the metrics that are essential for evaluating the effectiveness of the architecture team's work and ensuring alignment with the project's goals.

## General Guidelines for the Architecture Sync Meeting

- Schedule the meeting monthly, preferably on Thursdays during the third week of the month, to review architecture metrics and progress.  
- Prepare and share necessary documents and reports with the team in advance.  
- Run architecture assessment forms with the architects’ team before the meeting.  
- Use the Architecture Metrics Template.xlsx as the primary document to track issues, risks, and action.  
- Discuss technical issues identified through the forms and SonarQube metrics reports.  
- For any risks detected, ensure Jira tickets are created with detailed descriptions and proper labeling.  
- Allocate time for each project to review their technical issues and metrics slides.  
- Encourage open and constructive discussion to identify blockers and improvement opportunities.  
- Document decisions, action items, and responsible persons clearly during the meeting.  
- Share meeting notes with all participants promptly after the sync.  
- Continuously improve the process based on feedback from these sync meetings.  
- During the second-to-last week of each month, a PowerPoint presentation will be generated with the latest project metrics
- The presentation will be stored in the following SharePoint path:
 - **Folder Structure:**
  - `Architecture Reports/`
    - `{{Year (YYYY)}}/`
      - `Business Performance Metrics - {{Month (MMM)}}.pptx`

- **Example:**
  - `Architecture Reports/`
    - `2025/`
      - `Business Performance Metrics - Sep.pptx`

- You will receive an email notification with a direct link to the presentation.: [Metrics Reports SharePoint Folder](https://applaudostudios.sharepoint.com/sites/applaudo-technology/agile-facilitator-team/Shared%20Documents/Forms/AllItems.aspx?csf=1&web=1&e=czLxkV&CID=f0c1217f%2D76f2%2D4502%2D8874%2Da1df2b0f52f2&FolderCTID=0x0120008DD540FDAF8D8E4594A9C0D1105398DD&id=%2Fsites%2Fapplaudo%2Dtechnology%2Fagile%2Dfacilitator%2Dteam%2FShared%20Documents%2FProject%20Execution%2FMetrics%20Reports)  
- You will receive an email notification with a link to the presentation—please keep an eye out for it.  
- If you do not receive the email, check the folder manually.  
- If the file is not there, contact the Engineering Delivery Manager: `@LuisChong`.

- The designated architect for each project is responsible for filling in their project’s information in the corresponding monthly presentation.

**Important:**  
- If a project has more than one Business Architect, they should rotate the presentation of the metrics.  
- If a project has both a Business Architect and an Application Architect, the presentation responsibility should also rotate between them.  
- The rotation should follow a monthly schedule, clearly defined and agreed upon by the architects’ team. 
- When someone is unavailable (due to vacation or other reasons), there must always be a designated responsible person to ensure continuity.  

**Accountabilities of the Architect Responsible for Presenting Metrics at the SteerCo Meeting:**  
1. Ensure that the sync meeting takes place on Thursday (before the SteerCo meeting, which is usually on Monday or Tuesday).  
2. Guarantee that both the Excel file and the PowerPoint presentation are ready with all the required information.  
3. Ensure the folder structure and file naming standards are properly maintained.  
4. If deemed necessary, the presenting architect can invite the architect who raised a specific issue to the meeting, since the committee may question or request further details. The presenting architect must be able to respond assertively.  
   - This scenario is particularly relevant when there is more than one Business Architect involved.

## Flow

1. Decide with the Architecture team on a monthly Architecture sync (Thursday) to review metrics.  
    - Run the architecture forms with the team of architects:  
        - **AA:**  
            - [Tech Quality - Application Architecture - Solution](https://forms.office.com/Pages/ResponsePage.aspx?id=rydkDp6r9kqfb7wJj0cNdUraengFZzhCvmHh_6rkZvtUNjlNWDZJVUI4NU5WWUo4VTgxTEZRTVJMVyQlQCN0PWcu&origin=Invitation&channel=0)  
            - [Tech Quality - Application Architecture - Process](https://forms.office.com/Pages/ResponsePage.aspx?id=rydkDp6r9kqfb7wJj0cNdUraengFZzhCvmHh_6rkZvtUOEpEQTNHUE5OVFVFVU5RUEJaNDhVN05IQyQlQCN0PWcu&origin=Invitation&channel=0)  
        - **BA:**  
            - [Tech Quality - Solution Architecture - Architecture Execution Health](https://forms.office.com/Pages/ResponsePage.aspx?id=rydkDp6r9kqfb7wJj0cNdUraengFZzhCvmHh_6rkZvtUMTZFMk1UOVIxWEFTUFIxOVVIQjNTTENQTCQlQCN0PWcu&origin=Invitation&channel=0)  
            - [Tech Quality - Solution Architecture - Strategic Foundations Checklist](https://forms.office.com/Pages/ResponsePage.aspx?id=rydkDp6r9kqfb7wJj0cNdUraengFZzhCvmHh_6rkZvtUNzA2NVlIVFhYM044SkVMSTgzM0VYMjdKRiQlQCN0PWcu&origin=Invitation&channel=0)  

2. Clone the Excel template from [Architecture Metrics Template.xlsx](https://applaudostudios.sharepoint.com/:x:/r/sites/SoftwareArchitectureTeam/Shared%20Documents/Metrics/Architecture%20Metrics%20Template.xlsx?d=wd663fdd9fce4455bb4b60d966fd6ef9a&csf=1&web=1&e=AdRwty) and paste it into the following folder structure in the architecture SharePoint ([Metrics](https://applaudostudios.sharepoint.com/:f:/r/sites/SoftwareArchitectureTeam/Shared%20Documents/Metrics?csf=1&web=1&e=xtWUfG)):  
    - `Documents/Metrics/{clientName}/{year}-{month}/Architecture Metrics Template.xlsx`  

    - Based on the results of the forms and the current status of the project, decide on the technical issues and define action to be added to the **Architecture Metrics Template.xlsx**.  
        - Fill the technical issues tab for the project with the information gathered.  
          - For each repository, fill one row in the Technical Issues sheet, documenting all issues related to that repo.

        - If the issue type is **"Risk"**:  
            - Create a Jira ticket with the following data:  
                - **Description:** There must be a description of the risk with the **respective recommendations to mitigate the risk**, or a link to Confluence containing an ADR (Architectural Decision Record) with that information.  
                - **Labels:** **`risk`**, **`architecture`**  
                - **Assignee:** The architect  
                - **Reportee:** The architect  
                - **Priority:** Based on the risk assessment  
                - **Type:** Administrative Task  

   - Extract the SonarQube project metrics information from the SonarQube report at [Grafana Dashboard](https://dashboards.applaudo.com/d/b770fa1c-300a-48b7-9324-391bd589a497/steerco-metrics?orgId=1) and update the **Architecture Metrics Template.xlsx** accordingly.  

      - **Note:** First, select the appropriate SonarQube project corresponding to your client or product.  
      - Then, obtain the metric graphs and data for each individual repository within the project.  
      - Export and compile all relevant repository data to ensure the Excel template reflects the complete current status of the project.

---

### How to Fill the Architecture Metrics Template.xlsx

---

### Architecture Metrics Template Tabs Overview

The **Architecture Metrics Template.xlsx** consists of **three main tabs** that must be completed and reviewed regularly. These tabs correspond to the key sections of the SteerCo presentation and include:

1. **Project Information** – General project details like client name, date, and SteerCo dashboard link.  
2. **Technical Issues** – Detailed list of technical problems per repository with impact, action plan, and status.  
3. **Repositories** – Overview of repositories with associated quality metrics from tools like SonarQube.

This tab structure ensures a clear and organized flow from data capture to presentation.

---
### Project Information tab

The **Project Information** sheet contains key metadata about the project, including client name, date, and link to the SteerCo metrics dashboard.

Typical example:

| Client                | Date       | SteerCo Metrics Dashboard                                                              |
|-----------------------|------------|----------------------------------------------------------------------------------------|
| BAES - Banco Atlantida | 7/22/2025  | [SteerCo Metrics Dashboard](https://dashboards.applaudo.com/d/b770fa1c-300a-48b7-9324-391bd589a497/steerco-metrics) |

- **Access the Project Information tab here:**  
[Open Project Information Tab](https://applaudostudios.sharepoint.com/:x:/r/sites/SoftwareArchitectureTeam/Shared%20Documents/Metrics/Architecture%20Metrics%20Template.xlsx?d=wd663fdd9fce4455bb4b60d966fd6ef9a&csf=1&web=1&e=sqOEQ3&nav=MTVfezAwMDAwMDAwLTAwMDEtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMH0)

{{< asset-img src="/assets/images/excel-project-information-sheet.png" alt="Project Information Sheet Example" >}}

Please ensure this section is kept accurate as it links directly to live dashboard data.

### Technical Issues Tab

The template has a tab called **Technical Issues**, which may look like the following image:
{{< asset-img src="/assets/images/excel-technical-issues-tab.png" alt="Technical Issues tab example in Architecture Metrics Template" >}}
- **Access the Technical Issues tab here:**  
[Open Technical Issues Tab](https://applaudostudios.sharepoint.com/:x:/r/sites/SoftwareArchitectureTeam/Shared%20Documents/Metrics/Architecture%20Metrics%20Template.xlsx?d=wd663fdd9fce4455bb4b60d966fd6ef9a&csf=1&web=1&e=XOG03W&nav=MTVfe0ZGNTgxMzU0LUQ3ODUtNDUzNS1BQUZELTQzMDJBRDExOUE3NH0)

{{< asset-img src="/assets/images/excel-technical-issues2-tab.png" alt="Technical Issues tab example in Architecture Metrics Template" >}}

After filling the main table, update the related pivot tables to refresh the charts, which will later be used in the presentation slides, as shown below:

{{< asset-img src="/assets/images/excel-pivot-tables-update.png" alt="Pivot tables updated for charts in Excel template" >}}

### Repositories Tab

#### Importing Repository Data for the Repositories Tab

On the **Repositories** tab, enter the client name and retrieve data for each repository associated with the project.
For each repository, fill one row in the sheet with the related data so that the sheet looks like the following:

{{< asset-img src="/assets/images/excel-sonarqube-metrics.png" alt="Completed repositories data in Excel template" >}}

- **Access the Repositories tab here:**  
[Open Repositories Tab](https://applaudostudios.sharepoint.com/:x:/r/sites/SoftwareArchitectureTeam/Shared%20Documents/Metrics/Architecture%20Metrics%20Template.xlsx?d=wd663fdd9fce4455bb4b60d966fd6ef9a&csf=1&web=1&e=omafFE&nav=MTVfe0NENjcxQzlGLTA2RkEtNEY1Ny1BMUVCLUUyOEJCODc3MTZBQn0)

{{< asset-img src="/assets/images/excel-repositories-client-data.png" alt="Client repository data example in Excel Repositories tab" >}}

To fill the **Repositories** tab accurately, follow these steps to export the necessary data:

1. Visit the SteerCo metrics dashboard at:  
   [SteerCo Metrics Dashboard](https://dashboards.applaudo.com/d/b770fa1c-300a-48b7-9324-391bd589a497/steerco-metrics)

2. Select the appropriate **Jira Project ID** from the dropdown at the top, as shown below:

{{< asset-img src="/assets/images/jira-project-id-selection.png" alt="Selecting Jira Project ID on Dashboard" >}}

3. On the SonarQube Projects table, click the three-dot menu icon as displayed here:

{{< asset-img src="/assets/images/three-dots-menu.png" alt="SonarQube Projects Table Three-dot Menu" >}}

4. Navigate to **Inspect > Data** within the menu:

{{< asset-img src="/assets/images/inspect-data.png" alt="Inspect Data option in SonarQube" >}}

5. In the Inspect dialog, go to the **Data** tab and click the **Download CSV** button to export the repository data as shown:

{{< asset-img src="/assets/images/download-csv-button.png" alt="Download CSV button for SonarQube project data" >}}

6. Use the downloaded CSV file to copy the relevant repository data into the **Repositories** tab of the Architecture Metrics Template.xlsx.

This process ensures you have the latest repository metrics for accurate and up-to-date reporting.

---

### How to Prepare the SteerCo Presentation (PPT)

The SteerCo presentation consists of **three main slides** which must be prepared using data from the **Architecture Metrics Template.xlsx** and SonarQube reports.

---

#### 1. Code Quality [Sonar Project] - Metrics Slide

- The first slide to fill, **containing instructions** in the speaker notes and guidance at the top.

{{< asset-img src="/assets/images/steerco-ppt-overview-notes.png" alt="SteerCo PPT Overview Slide Speaker Notes" >}}


#### Charts and Graphs

- For each repository, select the corresponding SonarQube Project Trend chart as shown in the example below.  
- Download each chart separately to include it in the specific slide dedicated to that repository in the SteerCo presentation.  
- This ensures that the presentation accurately reflects the code quality trends over the last three months for all repositories.

{{< asset-img src="/assets/images/sonarqube-project-trend-example.png" alt="SonarQube Project Trend Chart Example" >}}

- Includes the **SonarQube Project Trend** chart showing bugs, coverage, and open issues over the last 3 months.  

{{< asset-img src="/assets/images/steerco-ppt-sonarqube-trend.png" alt="SonarQube Project Trend Chart" >}}

- Also includes an overall merged view of all SonarQube metrics for the project, along with the repository-specific metrics and the trend image, as illustrated below. This combined view will appear in the SteerCo presentation slide to provide a comprehensive overview of the project's code quality.

> **IMPORTANT NOTE:**  
> Please strictly follow the color usage instructions provided both in the speaker notes and at the top of the slide. Adhering to these guidelines ensures consistency and clarity in presenting the metrics.

{{< asset-img src="/assets/images/steerco-ppt-sonarqube-overall-metrics.png" alt="SonarQube Project Overall Metrics Overview" >}}

---

#### 2. Overview [Project] Slide (Technical Issues Summary)

Please copy and paste **charts** from the related pivot tables, which were refreshed previously, as shown below:

{{< asset-img src="/assets/images/excel-pivot-tables-update.png" alt="Pivot tables updated for charts in Excel template" >}}

This slide provides a graphical summary of the **Technical Issues** by type and status, including:  
- Task type distribution (e.g., Risk, Improvement, Technical Debt)  
- Status by impact (e.g., Pending, In Progress, Done, Cancelled) broken down by priority levels

{{< asset-img src="/assets/images/steerco-ppt-technical-issues-summary-charts.png" alt="SteerCo PPT Technical Issues Summary Charts" >}}

---

#### 3. Issues [Project] Slide (Technical Issues Details Table)

Use the data populated in the **Technical Issues** tab of the project’s Excel template to complete this slide:

{{< asset-img src="/assets/images/excel-technical-issues-tab.png" alt="Technical Issues tab example in Architecture Metrics Template" >}}

- Detailed list of technical issues extracted from the Excel sheet.  
- Columns include: Description, Impact, Action Plan, Status, Type, and Created Date.  
- **Note:** **If there are too many issues, include only** those with **Critical** and **High** priority.

{{< asset-img src="/assets/images/steerco-ppt-technical-issues-details-table.png" alt="SteerCo PPT Technical Issues Details Table" >}}

---

### Best Practices for the SteerCo Presentation

- Use clear, concise text and consistent formatting.  
- Keep slides uncluttered; use multiple slides if needed.  
- Verify if current data and the excel matches with SonarQube.  
- Coordinate proactively with team members if more input is needed.

---

### Coordination and Follow-up

- The presenting architect should follow up with the Agile Facilitator if the SteerCo PPT template is not shared by Friday morning.  
- Invite architects who raised issues to support detailed Q&A during the presentation as needed.

---

### Monthly Responsibilities and Deliverables Summary

| Week / Day(s)               | Responsible            | Deliverables / Activity                                                                                   | Notes                                                                                      |
|----------------------------|------------------------|------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------|
| Week 3 (Thursday)          | Architecture Team      | Architecture sync meeting to review metrics and align actions.                                            | Suggested day for the monthly sync.                                                       |
| Week 3 (Post-meeting)      | Designated Architect   | Manually complete the SteerCo presentation using the latest metrics and decisions.                        | Use the file located in the [Metrics Reports SharePoint Folder](https://applaudostudios.sharepoint.com/sites/applaudo-technology/agile-facilitator-team/Shared%20Documents/Forms/AllItems.aspx?csf=1&web=1&e=czLxkV&CID=f0c1217f%2D76f2%2D4502%2D8874%2Da1df2b0f52f2&FolderCTID=0x0120008DD540FDAF8D8E4594A9C0D1105398DD&id=%2Fsites%2Fapplaudo%2Dtechnology%2Fagile%2Dfacilitator%2Dteam%2FShared%20Documents%2FProject%20Execution%2FMetrics%20Reports). If not found, consult the Agile Facilitator. |
| Week 4 (Monday)            | Designated Architect   | Ensure the SteerCo presentation is finalized and ready for delivery.                                     | Deadline to have the PPT completed.                                                       |
| Week 4 (Monday or Tuesday) | Designated Architect   | Present the metrics at the SteerCo meeting. May request support from other architects if needed.          | Same person as above, acting as the presenter.                                            |


## Troubleshooting

1. **Verify Grafana permissions:**  
   - Visit: [cqm-overview-prod - CQM - Prod - SteerCo Metrics](https://dashboards.applaudo.com/d/b770fa1c-300a-48b7-9324-391bd589a497/steerco-metrics?orgId=1)  

   - If you see an error like the following:  

     {{< asset-img src="/assets/images/ea-grafana-login-error-login.PNG" alt="Grafana login error" >}}  

   - **Submit a ticket:** 
   [New Request - IT Applaudo applaudostudios.com](https://servicedesk.applaudostudios.com/app/itdesk/ui/requests/add?reqTemplate=137355000002080909) 
   
     ``` 
     Ticket: New Request - IT (Applaudo applaudostudios.com)
     Subject: Grafana Access
     Description: I need access to Grafana (https://dashboards.applaudo.com/).
     Application: Grafana-Dashboards
     Role: Read
     ```

2. **SonarQube config:**  
   - Validate repository variables against SonarQube configuration to ensure consistency and accuracy.