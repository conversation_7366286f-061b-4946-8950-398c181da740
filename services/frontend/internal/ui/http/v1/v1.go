// Package v1 contains the HTTP handlers for version 1 of the API.
package v1

import (
	"app/frontend/internal/ui"
	"app/frontend/internal/ui/http/v1/admin"
	"app/frontend/internal/ui/http/v1/auth"
	aiprompt "app/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt"
	"app/frontend/internal/ui/http/v1/cqm/sonarqube/company_clients"
	"app/frontend/internal/ui/http/v1/cqm/sonarqube/issues"
	"app/frontend/internal/ui/http/v1/cqm/sonarqube/jira_projects"
	"app/frontend/internal/ui/http/v1/cqm/sonarqube/projects"
	"app/frontend/internal/ui/http/v1/health"
	"app/frontend/internal/ui/http/v1/home"
	"app/frontend/internal/ui/http/v1/webhooks"
	"app/frontend/internal/ui/http/v1/wiki"

	"app/frontend/internal/ui/http/v1/mcp"
)

func RegisterRoutes(app *ui.App) {
	// Register your handlers here
	home.RegisterRoutes(app)
	wiki.RegisterRoutes(app)
	health.RegisterRoutes(app)
	auth.RegisterRoutes(app)
	issues.RegisterRoutes(app)
	projects.RegisterRoutes(app)
	jira_projects.RegisterRoutes(app)
	company_clients.RegisterRoutes(app)
	aiprompt.RegisterRoutes(app)
	webhooks.RegisterRoutes(app)
	admin.RegisterRoutes(app)
	mcp.RegisterRoutes(app)
}
