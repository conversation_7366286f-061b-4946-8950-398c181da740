package mcp_test

import (
	"context"
	"fmt"
	"log"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// GreetingInput defines the input parameters for the greeting tool
type GreetingInput struct {
	Name string `json:"name" jsonschema:"the name of the person to greet"`
}

// GreetingOutput defines the output structure for the greeting tool
type GreetingOutput struct {
	Greeting string `json:"greeting" jsonschema:"the greeting message"`
}

// Example demonstrates how to use the greeting MCP tool
func Example() {
	// Create an MCP server implementation
	impl := &mcp.Implementation{
		Name:    "greeting-server",
		Title:   "Greeting MCP Server",
		Version: "1.0.0",
	}

	// Create the MCP server
	srv := mcp.NewServer(impl, nil)

	// Add the greeting tool (this would normally be done in RegisterRoutes)
	mcp.AddTool(srv, &mcp.Tool{
		Name:        "greeting",
		Description: "Generate a personalized greeting message",
	}, greetingHandler)

	// In a real scenario, you would call the tool through the MCP protocol
	// Here we demonstrate the handler directly for testing purposes
	ctx := context.Background()
	req := &mcp.CallToolRequest{}
	input := GreetingInput{Name: "World"}

	result, output, err := greetingHandler(ctx, req, input)
	if err != nil {
		log.Fatalf("Error calling greeting handler: %v", err)
	}

	fmt.Printf("Greeting: %s\n", output.Greeting)
	fmt.Printf("Content: %s\n", result.Content[0].(*mcp.TextContent).Text)

	// Output:
	// Greeting: Hello, World! Welcome to SA Intranet!
	// Content: Hello, World! Welcome to SA Intranet!
}

// greetingHandler is a copy of the handler for testing purposes
func greetingHandler(ctx context.Context, req *mcp.CallToolRequest, input GreetingInput) (*mcp.CallToolResult, GreetingOutput, error) {
	greeting := "Hello, " + input.Name + "! Welcome to SA Intranet!"

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			&mcp.TextContent{Text: greeting},
		},
	}, GreetingOutput{Greeting: greeting}, nil
}
