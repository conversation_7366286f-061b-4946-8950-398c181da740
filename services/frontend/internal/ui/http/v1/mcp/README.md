# MCP (Model Context Protocol) Server

This package implements an MCP server for the SA Intranet application, providing tools that AI assistants can use to interact with the system.

## Overview

The MCP server is mounted at `/mcp` and supports the Model Context Protocol for communication with AI assistants. It provides a standardized way for AI tools to access and interact with SA Intranet functionality.

## Available Tools

### Greeting Tool

**Name:** `greeting`  
**Description:** Generate a personalized greeting message

**Input Parameters:**
- `name` (string): The name of the person to greet

**Output:**
- `greeting` (string): The personalized greeting message

**Example Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "greeting",
    "arguments": {
      "name": "<PERSON>"
    }
  }
}
```

**Example Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "Hello, <PERSON>! Welcome to SA Intranet!"
      }
    ]
  }
}
```

## Endpoints

- `GET /mcp` - MCP protocol endpoint for tool discovery and communication
- `POST /mcp` - MCP protocol endpoint for tool execution
- `GET /.well-known/oauth-protected-resource` - OAuth resource discovery endpoint

## Authentication

The MCP server supports OAuth 2.0 authentication. The OAuth configuration is available at the `/.well-known/oauth-protected-resource` endpoint.

## Development

### Adding New Tools

To add a new MCP tool:

1. Define input and output structures:
```go
type MyToolInput struct {
    Parameter string `json:"parameter" jsonschema:"description of parameter"`
}

type MyToolOutput struct {
    Result string `json:"result" jsonschema:"description of result"`
}
```

2. Implement the tool handler:
```go
func myToolHandler(ctx context.Context, req *mcp.CallToolRequest, input MyToolInput) (*mcp.CallToolResult, MyToolOutput, error) {
    // Tool logic here
    result := "processed: " + input.Parameter
    
    return &mcp.CallToolResult{
        Content: []mcp.Content{
            &mcp.TextContent{Text: result},
        },
    }, MyToolOutput{Result: result}, nil
}
```

3. Register the tool in `RegisterRoutes`:
```go
mcp.AddTool(srv, &mcp.Tool{
    Name:        "my-tool",
    Description: "Description of what the tool does",
}, myToolHandler)
```

### Testing

The package includes comprehensive tests covering:
- Tool handler functionality
- Input/output validation
- MCP server setup
- OAuth endpoint responses

Run tests with:
```bash
go test ./internal/ui/http/v1/mcp -v -cover
```

### Example Usage

See `example_test.go` for a complete example of how to use the greeting tool.

## Dependencies

- [Model Context Protocol Go SDK](https://github.com/modelcontextprotocol/go-sdk) - Official MCP implementation
- SA Intranet UI framework - Application routing and configuration

## Architecture

The MCP server follows the Clean Architecture principles used throughout the SA Intranet project:
- **Handlers**: Tool implementations with business logic
- **Models**: Input/output data structures
- **Registration**: Route and dependency setup

This ensures maintainability and testability while providing a clean interface for AI assistant integration.
