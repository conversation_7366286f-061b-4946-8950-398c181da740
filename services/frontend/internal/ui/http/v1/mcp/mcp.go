package mcp

import (
	"net/http"

	"app/frontend/internal/ui"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

func RegisterRoutes(app *ui.App) {
	// --- MCP Server setup ---
	impl := &mcp.Implementation{
		Name:    "my-mcp",
		Title:   "Example MCP Server mounted on existing mux",
		Version: "1.0.0",
	}

	// Create the MCP server with your implementation
	srv := mcp.NewServer(impl, nil)

	mcpHandler := mcp.NewStreamableHTTPHandler(func(req *http.Request) *mcp.Server {
		return srv
	}, nil)

	// Register tools/resources/etc. on the server as needed
	// e.g.: srv.AddTool(myTool)

	// Create the HTTP handler for the MCP server

	middleware := func(next http.Handler) http.Handler {
		return mcpHandler
	}

	// Mount the MCP handler at /mcp
	app.Router().GET("/mcp", nil, middleware)
	app.Router().POST("/mcp", nil, middleware)

	app.Router().GET("/.well-known/oauth-protected-resource", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{
		  "resource": "` + app.AppConfig.OAUTH_PROTECTED_RESOURCE_URL + `",
		  "authorization_servers": [
		    "` + app.AppConfig.OAUTH_PROTECTED_RESOURCE_SERVER + `"
		  ]
		}`))
	})
}
