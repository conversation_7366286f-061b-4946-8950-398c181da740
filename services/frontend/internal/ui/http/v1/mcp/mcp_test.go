package mcp

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"app/frontend/internal/ui"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

const (
	testMCPName    = "test-mcp"
	testMCPTitle   = "Test MCP Server"
	testMCPVersion = "1.0.0"
)

func TestGreetingHandler(t *testing.T) {
	tests := []struct {
		name     string
		input    GreetingInput
		expected string
		wantErr  bool
	}{
		{
			name:     "valid greeting with name",
			input:    GreetingInput{Name: "Alice"},
			expected: "Hello, <PERSON>! Welcome to SA Intranet!",
			wantErr:  false,
		},
		{
			name:     "valid greeting with empty name",
			input:    GreetingInput{Name: ""},
			expected: "Hello, ! Welcome to SA Intranet!",
			wantErr:  false,
		},
		{
			name:     "valid greeting with special characters",
			input:    GreetingInput{Name: "<PERSON> & María"},
			expected: "Hello, <PERSON> & María! Welcome to SA Intranet!",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			req := &mcp.CallToolRequest{}

			result, output, err := greetingHandler(ctx, req, tt.input)

			if (err != nil) != tt.wantErr {
				t.Errorf("greetingHandler() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err != nil {
				return // Skip validation if error expected
			}

			verifyGreetingResult(t, result, output, tt.expected)
		})
	}
}

func verifyGreetingResult(t *testing.T, result *mcp.CallToolResult, output GreetingOutput, expected string) {
	t.Helper()

	// Check the output structure
	if output.Greeting != expected {
		t.Errorf("greetingHandler() output.Greeting = %v, want %v", output.Greeting, expected)
	}

	// Check the result content
	if result == nil {
		t.Error("greetingHandler() result is nil")
		return
	}

	if len(result.Content) != 1 {
		t.Errorf("greetingHandler() result.Content length = %v, want 1", len(result.Content))
		return
	}

	textContent, ok := result.Content[0].(*mcp.TextContent)
	if !ok {
		t.Error("greetingHandler() result.Content[0] is not TextContent")
		return
	}

	if textContent.Text != expected {
		t.Errorf("greetingHandler() result.Content[0].Text = %v, want %v", textContent.Text, expected)
	}
}

func TestGreetingInputValidation(t *testing.T) {
	// Test that the struct tags are properly defined
	input := GreetingInput{Name: "Test"}

	// This is a basic test to ensure the struct is properly defined
	if input.Name != "Test" {
		t.Errorf("GreetingInput.Name = %v, want Test", input.Name)
	}
}

func TestGreetingOutputValidation(t *testing.T) {
	// Test that the struct tags are properly defined
	output := GreetingOutput{Greeting: "Test greeting"}

	// This is a basic test to ensure the struct is properly defined
	if output.Greeting != "Test greeting" {
		t.Errorf("GreetingOutput.Greeting = %v, want Test greeting", output.Greeting)
	}
}

func TestMCPServerCreation(t *testing.T) {
	// Test that we can create an MCP server with the greeting tool
	impl := &mcp.Implementation{
		Name:    testMCPName,
		Title:   testMCPTitle,
		Version: testMCPVersion,
	}

	srv := mcp.NewServer(impl, nil)
	if srv == nil {
		t.Fatal("Failed to create MCP server")
	}

	// Add the greeting tool
	mcp.AddTool(srv, &mcp.Tool{
		Name:        "greeting",
		Description: "Generate a personalized greeting message",
	}, greetingHandler)

	// Test that the server was created successfully
	// This is a basic smoke test to ensure the MCP setup works
	t.Log("MCP server created successfully with greeting tool")
}

func TestOAuthResourceResponse(t *testing.T) {
	// Test the OAuth protected resource endpoint response format
	config := &ui.AppConfig{
		OAUTH_PROTECTED_RESOURCE_URL:    "http://test.com/mcp",
		OAUTH_PROTECTED_RESOURCE_SERVER: "http://auth.test.com",
	}

	// Create a test request
	req := httptest.NewRequest("GET", "/.well-known/oauth-protected-resource", nil)
	w := httptest.NewRecorder()

	// Create the handler function
	handler := func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{
		  "resource": "` + config.OAUTH_PROTECTED_RESOURCE_URL + `",
		  "authorization_servers": [
		    "` + config.OAUTH_PROTECTED_RESOURCE_SERVER + `"
		  ]
		}`))
	}

	// Call the handler
	handler(w, req)

	// Check the response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	contentType := w.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type application/json, got %s", contentType)
	}

	// Basic check that the response contains expected values
	body := w.Body.String()
	if !contains(body, config.OAUTH_PROTECTED_RESOURCE_URL) {
		t.Errorf("Response body should contain resource URL: %s", config.OAUTH_PROTECTED_RESOURCE_URL)
	}
	if !contains(body, config.OAUTH_PROTECTED_RESOURCE_SERVER) {
		t.Errorf("Response body should contain auth server: %s", config.OAUTH_PROTECTED_RESOURCE_SERVER)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsHelper(s, substr))))
}

func containsHelper(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func TestRegisterRoutesComponents(t *testing.T) {
	// Test individual components that make up RegisterRoutes
	// This provides better coverage without complex mocking

	t.Run("MCP Implementation Creation", func(t *testing.T) {
		impl := &mcp.Implementation{
			Name:    testMCPName,
			Title:   testMCPTitle,
			Version: testMCPVersion,
		}

		if impl.Name != testMCPName {
			t.Errorf("Expected Name to be '%s', got %s", testMCPName, impl.Name)
		}
		if impl.Title != testMCPTitle {
			t.Errorf("Expected Title to be '%s', got %s", testMCPTitle, impl.Title)
		}
		if impl.Version != testMCPVersion {
			t.Errorf("Expected Version to be '%s', got %s", testMCPVersion, impl.Version)
		}
	})

	t.Run("MCP Server Creation", func(t *testing.T) {
		impl := &mcp.Implementation{
			Name:    testMCPName,
			Title:   testMCPTitle,
			Version: testMCPVersion,
		}

		srv := mcp.NewServer(impl, nil)
		if srv == nil {
			t.Fatal("Failed to create MCP server")
		}
	})

	t.Run("MCP Tool Addition", func(t *testing.T) {
		impl := &mcp.Implementation{
			Name:    testMCPName,
			Title:   testMCPTitle,
			Version: testMCPVersion,
		}

		srv := mcp.NewServer(impl, nil)

		// This should not panic
		mcp.AddTool(srv, &mcp.Tool{
			Name:        "greeting",
			Description: "Generate a personalized greeting message",
		}, greetingHandler)

		t.Log("Successfully added greeting tool to MCP server")
	})

	t.Run("MCP HTTP Handler Creation", func(t *testing.T) {
		impl := &mcp.Implementation{
			Name:    testMCPName,
			Title:   testMCPTitle,
			Version: testMCPVersion,
		}

		srv := mcp.NewServer(impl, nil)

		handler := mcp.NewStreamableHTTPHandler(func(req *http.Request) *mcp.Server {
			return srv
		}, nil)

		if handler == nil {
			t.Fatal("Failed to create MCP HTTP handler")
		}
	})
}
